{"type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build:qa": "vite build --mode qa", "build:stage": "vite build --mode stage", "build:prod": "vite build --mode prod", "preview": "vite preview"}, "dependencies": {"@breezystack/lamejs": "^1.2.7", "@tci18n/vue3": "^2.0.1", "axios": "^1.11.0", "js-audio-recorder": "^1.0.7", "js-cookie": "^3.0.5", "less": "^4.4.0", "marked": "^16.1.2", "mitt": "^3.0.1", "pinia": "^3.0.3", "uuid": "^11.1.0", "vant": "^4.9.21", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node20": "^20.1.6", "@types/js-cookie": "^3.0.6", "@types/node": "^24.3.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "chalk": "^5.6.0", "compressing": "^2.0.0", "vite": "^7.0.6"}}