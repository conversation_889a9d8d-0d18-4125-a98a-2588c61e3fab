<template>
  <van-popup v-model:show="isShow" ref="vanPopupRef" position="bottom" round safe-area-inset-bottom :overlay="isOverlay"
    :close-on-click-overlay="false" :style="{ display: isOverlay ? 'block' : 'none' }">
    <div class="popWrapper" ref="popContentRef" :style="{ 'height': popHeight + 'px' }">
      <div class="popTitle">选择人员</div>
      <div class="popLoading" v-if="isLoading">
        <van-loading v-if="isLoading" type="spinner" />
      </div>
      <iframe ref="iframeRef" :src="iframeSrc" width="100%" @load="iframeOnload"></iframe>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { getToken } from '@/utils/token'
const emit = defineEmits(['select'])

const vanPopupRef = ref(null)
const isShow = ref(true)
const popHeight = window.innerHeight * 0.8
const isLoading = ref(true)
const iframeRef = ref(null)
const popContentRef = ref(null)
const isOverlay = ref(false)
const iframeSrc = `${import.meta.env.VITE_IFRAME_DOMAIN}/newapp/pages/common/choice/index?type=1000&locale=zh&token=${getToken()}`

async function open() {
  console.log('SelectPassenger open() 被调用')
  console.log('iframe URL:', iframeSrc)

  isOverlay.value = true
  isShow.value = true
  // 打开弹窗时，把 iframe 移进弹窗容器
  await nextTick()
  if (!popContentRef.value.querySelector('iframe')) {
    popContentRef.value.appendChild(iframeRef.value)
    iframeRef.value.style.visibility = 'visible'
    iframeRef.value.style.position = 'static'
    console.log('iframe 已添加到弹窗容器')
  }
}

function iframeOnload() {
  console.log('iframe 加载完成')
  isLoading.value = false
  if (!isOverlay.value) {
    isShow.value = false
  }
}

function close() {
  isShow.value = false
}

function handleMessage(event) {
  console.log('handleMessage 收到原始事件:', event)
  console.log('事件来源:', event.origin)
  console.log('当前iframe域名:', import.meta.env.VITE_IFRAME_DOMAIN)

  if (!event) {
    console.error('handleMessage: 没有收到事件')
    return
  }

  if (!event.data) {
    console.error('handleMessage: 事件中没有data')
    console.log('完整事件对象:', event)
    return
  }

  console.log('事件data内容:', event.data)
  console.log('事件data类型:', typeof event.data)

  // 检查是否是我们期望的消息来源
  if (event.origin !== import.meta.env.VITE_IFRAME_DOMAIN) {
    console.warn('消息来源不匹配，忽略:', event.origin, '期望:', import.meta.env.VITE_IFRAME_DOMAIN)
    // 但是仍然处理，因为可能是开发环境的问题
  }

  if (!event.data.type) {
    console.error('handleMessage: 事件data中没有type字段')
    console.log('data内容:', event.data)
    return
  }

  console.log('收到消息: ', event.data, '来源: ', event.origin)
  console.log('准备emit select事件，数据:', event.data)

  emit('select', event.data)
}

onMounted(() => {
  console.log('SelectPassenger 组件已挂载，开始监听消息')

  // 添加全局消息监听器用于调试
  const debugMessageHandler = (event) => {
    console.log('全局消息监听器收到消息:', event)
  }

  window.addEventListener('message', handleMessage)
  window.addEventListener('message', debugMessageHandler)

  // 清理时移除调试监听器
  const originalRemoveEventListener = window.removeEventListener
  window.removeEventListener = function(type, listener, options) {
    if (type === 'message' && listener === handleMessage) {
      originalRemoveEventListener.call(this, 'message', debugMessageHandler, options)
    }
    return originalRemoveEventListener.call(this, type, listener, options)
  }
})

onUnmounted(() => {
  window.removeEventListener('message', handleMessage)
})

defineExpose({
  open,
  close
})
</script>

<style scoped>
iframe {
  margin: 0;
  padding: 0;
  display: block;
  border: 0;
  height: calc(100% - 40px);
}

.popWrapper {
  background: #fff;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.popTitle {
  padding-top: 12px;
  font-size: 18px;
  text-align: center;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  position: relative;
}

.popLoading {
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f6f8;
}
</style>
