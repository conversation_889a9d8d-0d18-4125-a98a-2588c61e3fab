<template>
  <div class="book-type-card">
    <div class="card-tit">{{ tipStr }}</div>
    <div class="options" v-if="[0, 2].includes(bookRoleType)">
      <div class="option" :class="{ 'active': selected === 'self' }" @click="bookForMyself"
        v-if="[0].includes(bookRoleType)">
        <span class="option-tit">本人预订</span>
        <span class="option-txt">为自己预订行程<template v-if="isTravelAppyBook">，使用差旅单</template></span>
      </div>
      <div class="option" :class="{ 'active': selected === 'other' }" @click="bookForOther"
        v-if="[0, 2].includes(bookRoleType)">
        <span class="option-tit">代他人预订</span>
        <span class="option-txt">为单个同事预订行程<template v-if="isTravelAppyBook">，使用差旅单</template></span>
      </div>
    </div>
    <SelectPassenger ref="selectPassengerRef" @select="handleSelect" />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { storeToRefs } from 'pinia'
import useUserStore from '@/stores/user'
import useChatStore from '@/stores/chat'
import { eventBus, EVENTS } from '@/utils/eventBus'
import SelectPassenger from '../selectPassenger.vue'

const props = defineProps({
  sid: String,
  msgId: String,
  message: Object,
  latestMsg: Boolean,
  selected: String
})

const userStore = useUserStore()
const { bookRoleType, isTravelAppyBook } = storeToRefs(userStore)
const tipStr = computed(() => {
  if (bookRoleType.value === 1) {
    return '识别到您仅有本人预订权限，已帮您自动选择'
  } else if (bookRoleType.value === 2) {
    return '识别到您仅有代他人预订权限，请点击选择帮谁代订'
  } else {
    return '❓ 请问您要仅预订自己的行程，还是要帮同事一并代订？'
  }
})

const chatStore = useChatStore()
const selectPassengerRef = ref(null)

const bookForMyself = () => {
  if (!props.latestMsg) return

  // 选择本人预订时，清除代订人信息
  userStore.clearSelectedPassenger()

  chatStore.updateMessage(props.sid, props.msgId, {
    content: `<a class="dt-json-container" data-json='{"type": "travelUser", "selected": "self"}'></a>`
  })

  eventBus.emit(EVENTS.SEND_CHAT_MSG, `<span class="travelUser" data-memberId="A111111">为自己预订</span>`)
}

const bookForOther = () => {
  if (!props.latestMsg) return

  selectPassengerRef.value?.open()
}

const handleSelect = (data) => {
  const approvalId = data.approvalId
  const memberId = data?.passengerList?.[0]?.passengerEmployeeId
  const memberName = data?.passengerList?.[0]?.selectName

  // 将代订人信息存储到全局状态
  userStore.setSelectedPassenger({
    memberId,
    memberName,
    approvalId
  })

  chatStore.updateMessage(props.sid, props.msgId, {
    content: `<a class="dt-json-container" data-json='{"type": "travelUser", "selected": "other"}'></a>`
  })

  selectPassengerRef.value?.close()

  eventBus.emit(EVENTS.SEND_CHAT_MSG, `<span class="travelUser" data-memberId="A222222">为${memberName}预定</span>`)
}
</script>

<style lang="less" scoped>
.book-type-card {
  background: #fff;
  box-shadow: 0px 0px 6px 0px #dee6ef;
  border-radius: 12px;
  padding: 10px 6px 15px 10px;

  .card-tit {
    font-weight: 500;
    font-size: 13px;
    color: #000;
    line-height: 22px;
  }

  .options {
    display: flex;
    align-items: center;
    margin-top: 10px;

    .option {
      width: 45%;
      height: 74px;
      border-radius: 12px;
      border: 1px solid #80aeff;
      box-sizing: border-box;
      margin-right: 5px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &:last-of-type {
        margin-right: 0;
      }

      .option-tit {
        font-weight: 600;
        font-size: 13px;
        color: #000;
        line-height: 14px;
      }

      .option-txt {
        margin-top: 5px;
        font-weight: 500;
        font-size: 12px;
        color: #878787;
        line-height: 14px;
      }

      &.active {
        background-color: #80aeff;

        .option-tit {
          color: #fff;
        }

        .option-txt {
          color: #fff;
        }
      }
    }
  }
}
</style>
