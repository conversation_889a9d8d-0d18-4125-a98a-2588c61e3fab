<template>
  <div class="travel-card">
    <div class="card-tit">识别到出行人有多个差旅单，请选择：</div>
    <div class="travels">
      <div class="travel" v-for="item in travelApplyList" :key="item.id" @click="handleSelect(item)">
        <div class="d-flex-between">
          <span class="travel-tit">{{ item.id }}</span>
          <span class="travel-state">通过</span>
        </div>
        <div class="travel-con">
          <div class="d-flex-align">
            <span class="con-label">出行人</span>
            <span class="con-main">{{ currentPassengerName }}</span>
          </div>
          <div class="d-flex-align mt4">
            <span class="con-label">起止时间</span>
            <span class="con-value">2025-07-30 - 2025-08-02</span>
          </div>
          <div class="d-flex-align mt4">
            <span class="con-label">地点</span>
            <span class="con-value">北京，保定</span>
          </div>
          <div class="d-flex-wrap">
            <span class="travel-tag main">预订权限</span>
            <span class="travel-tag">国内酒店</span>
            <span class="travel-tag">火车票</span>
          </div>
        </div>
        <div class="d-flex-end mt10">
          <span class="travel-btn" :class="{ 'active': item.id === selected }">
            <template v-if="item.id === selected">已选择</template>
            <template v-else>去使用</template>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { eventBus, EVENTS } from '@/utils/eventBus'
import useChatStore from '@/stores/chat'
import useUserStore from '@/stores/user'

const props = defineProps({
  sid: String,
  msgId: String,
  message: Object,
  latestMsg: Boolean,
  selected: String
})

const userStore = useUserStore()
const currentPassengerName = computed(() => userStore.getCurrentPassengerName)

const travelApplyList = ref([
  {
    id: 'SHJY0SQ2025070'
  },
  {
    id: 'SHJY0SQ2025071'
  },
  {
    id: 'SHJY0SQ2025072'
  }
])

const chatStore = useChatStore()
const handleSelect = (item) => {
  if (!props.latestMsg) return

  chatStore.updateMessage(props.sid, props.msgId, {
    content: `<a class="dt-json-container" data-json='{"type": "travelApplyNo", "selected": "${item.id}"}'></a>`
  })

  eventBus.emit(EVENTS.SEND_CHAT_MSG, `<span class="travelApplyNo">${item.id}</span>`)
}
</script>

<style lang="less" scoped>
.travel-card {
  background: #fff;
  box-shadow: 0px 0px 6px 0px #dee6ef;
  border-radius: 12px;
  padding: 12px 0 18px;

  .card-tit {
    padding: 0 11px;
    font-weight: 500;
    font-size: 13px;
    color: #000;
    line-height: 14px;
  }

  .travels {
    margin-top: 8px;
    overflow-x: auto;
    white-space: nowrap;

    .travel {
      display: inline-block;
      width: 70%;
      min-height: 170px;
      background: #fff;
      border-radius: 12px;
      border: 1px solid #80aeff;
      margin: 0 5px;
      padding: 10px;
      box-sizing: border-box;

      &:first-of-type {
        margin-left: 11px;
      }

      &:last-of-type {
        margin-right: 11px;
      }

      .travel-tit {
        font-weight: 500;
        font-size: 11px;
        color: #585858;
        line-height: 12px;
      }

      .travel-state {
        padding: 4px 5px;
        display: inline-block;
        background: #cffbd1;
        border-radius: 3px;
        font-weight: 500;
        font-size: 11px;
        color: #00c308;
        line-height: 12px;
      }

      .travel-con {
        margin-top: 8px;

        .con-label {
          width: 60px;
          font-weight: 500;
          font-size: 11px;
          color: #000;
          line-height: 14px;
        }

        .con-main {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-weight: 600;
          font-size: 14px;
          color: #4A75ED;
          line-height: 18px;
        }

        .con-value {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-weight: 500;
          font-size: 11px;
          color: #878787;
          line-height: 14px;
        }

        .travel-tag {
          margin-top: 7px;
          margin-right: 7px;
          background: #efefef;
          border-radius: 3px;
          font-weight: 500;
          font-size: 11px;
          color: #878787;
          line-height: 12px;
          padding: 4px 5px;

          &.main {
            background: #e3f2fd;
            color: #1876d1;
          }
        }
      }

      .travel-btn {
        background: #fff;
        border-radius: 3px;
        border: 1px solid #80aeff;
        font-weight: 500;
        font-size: 14px;
        color: #4a75ed;
        line-height: 16px;
        padding: 6px 8px;

        &.active {
          background: #80aeff;
          color: #fff;
        }
      }
    }
  }
}
</style>
