import { defineStore } from 'pinia'
import { getUserInfo } from '@/api/users'

interface UserInfo {
  token: string;
  employeeName: string;
  supplierName: string;
  isAdmin: number;
}

interface UserState {
  token: string;
  userInfo: any;
  loginType: string;
  configLasts: number;
  //权限类型
  bookRoleType: number;
  //是否差旅管控
  isTravelAppyBook: boolean;
  //代订人信息
  selectedPassenger: {
    memberId: string;
    memberName: string;
    approvalId: string;
  } | null;
}

const useUserStore = defineStore('userId', {
  state: (): UserState => {
    return {
      token: '',
      userInfo: {
        token: '',
        employeeName: '',
        supplierName: ''
      },
      loginType: '',
      configLasts: 0,
      bookRoleType: 0,
      isTravelAppyBook: false,
      selectedPassenger: null
    }
  },
  getters: {
    getToken(): string {
      return this.token
    },
    GetLoginType(state) {
      return state.loginType
    },
    getIsAdmin(state) {
      return state.userInfo.isAdmin
    },
    // 获取当前代订人姓名，默认为郭庆伟
    getCurrentPassengerName(state) {
      return state.selectedPassenger?.memberName || '郭庆伟'
    }
  },
  actions: {
    changeState(state: any) {
      this.userInfo = state
      // 处理权限
      const { hotelBookable, flightBookable, trainBookable, travelApplyBookType } = state
      this.isTravelAppyBook = travelApplyBookType == 1
      let roleList = [hotelBookable, flightBookable, trainBookable]
      const allOnes = roleList.every(item => item === 1)
      const allTwos = roleList.every(item => item === 2)
      // 判断权限值
      if (roleList.includes(0) || (roleList.includes(1) && roleList.includes(2))) {
        this.bookRoleType = 0
      } else if (allOnes) {
        this.bookRoleType = 1
      } else if (allTwos) {
        this.bookRoleType = 2
      }
    },
    setToken(token: string) {
      this.token = token
    },
    // 验证token
    async getUserInfo() {
      const res = await getUserInfo<any>()
      return res
    },
    // 设置代订人信息
    setSelectedPassenger(passenger: { memberId: string; memberName: string; approvalId: string } | null) {
      this.selectedPassenger = passenger
    },
    // 清除代订人信息
    clearSelectedPassenger() {
      this.selectedPassenger = null
    }
  }
})

export default useUserStore