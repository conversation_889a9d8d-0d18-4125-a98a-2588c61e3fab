import type { PluginOption, ConfigEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { conifgCompressDist } from './compressDist'

interface CustOptions {
  prodMock?: boolean;
}

export function createVitePlugins({ command, mode }: ConfigEnv, { prodMock }: CustOptions) {
  const vitePlugins: (PluginOption | PluginOption[])[] = [
    // vue支持
    vue()
  ]

  // 打包完成后生成zip
  if (command === 'build') {
    // 压缩静态资源
    vitePlugins.push(conifgCompressDist())
  }

  return vitePlugins
}
